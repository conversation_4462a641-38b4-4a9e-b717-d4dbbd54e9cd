// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7D5832A01F4E93312EEF22A8 /* Pods_AtitudeTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8FE591ADC19E4A6D37078825 /* Pods_AtitudeTests.framework */; };
		87A5B95D9D12940590D552C1 /* Pods_Atitude.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E1A128B442B729F5329C897 /* Pods_Atitude.framework */; };
		B2878B5CF922CCBCB77D0A27 /* Pods_Atitude_AtitudeUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ACF30694A05797A8FD4BE7BB /* Pods_Atitude_AtitudeUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A52D454A2E419ADA0089E3F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A52D45262E419AD80089E3F6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A52D452D2E419AD80089E3F6;
			remoteInfo = Atitude;
		};
		A52D45542E419ADA0089E3F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A52D45262E419AD80089E3F6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A52D452D2E419AD80089E3F6;
			remoteInfo = Atitude;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17AD4D34635C9ECFEC808E3D /* Pods-Atitude-AtitudeUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Atitude-AtitudeUITests.debug.xcconfig"; path = "Target Support Files/Pods-Atitude-AtitudeUITests/Pods-Atitude-AtitudeUITests.debug.xcconfig"; sourceTree = "<group>"; };
		2E1A128B442B729F5329C897 /* Pods_Atitude.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Atitude.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		43FE7A962C45A6A03416FCED /* Pods-Atitude.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Atitude.release.xcconfig"; path = "Target Support Files/Pods-Atitude/Pods-Atitude.release.xcconfig"; sourceTree = "<group>"; };
		6C95E9CFF7F2220B5FC3C69D /* Pods-AtitudeTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AtitudeTests.debug.xcconfig"; path = "Target Support Files/Pods-AtitudeTests/Pods-AtitudeTests.debug.xcconfig"; sourceTree = "<group>"; };
		7C43010EF285398031C2701C /* Pods-Atitude-AtitudeUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Atitude-AtitudeUITests.release.xcconfig"; path = "Target Support Files/Pods-Atitude-AtitudeUITests/Pods-Atitude-AtitudeUITests.release.xcconfig"; sourceTree = "<group>"; };
		8FE591ADC19E4A6D37078825 /* Pods_AtitudeTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AtitudeTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9041EE1287B798145387B777 /* Pods-Atitude.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Atitude.debug.xcconfig"; path = "Target Support Files/Pods-Atitude/Pods-Atitude.debug.xcconfig"; sourceTree = "<group>"; };
		A52D452E2E419AD80089E3F6 /* Atitude.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Atitude.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A52D45492E419ADA0089E3F6 /* AtitudeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AtitudeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A52D45532E419ADA0089E3F6 /* AtitudeUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AtitudeUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		ACF30694A05797A8FD4BE7BB /* Pods_Atitude_AtitudeUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Atitude_AtitudeUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CF907DEDD6B442BD4E451A56 /* Pods-AtitudeTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AtitudeTests.release.xcconfig"; path = "Target Support Files/Pods-AtitudeTests/Pods-AtitudeTests.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		A52D455B2E419ADA0089E3F6 /* Exceptions for "Atitude" folder in "Atitude" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = A52D452D2E419AD80089E3F6 /* Atitude */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A52D45302E419AD80089E3F6 /* Atitude */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				A52D455B2E419ADA0089E3F6 /* Exceptions for "Atitude" folder in "Atitude" target */,
			);
			path = Atitude;
			sourceTree = "<group>";
		};
		A52D454C2E419ADA0089E3F6 /* AtitudeTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = AtitudeTests;
			sourceTree = "<group>";
		};
		A52D45562E419ADA0089E3F6 /* AtitudeUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = AtitudeUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		A52D452B2E419AD80089E3F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				87A5B95D9D12940590D552C1 /* Pods_Atitude.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D45462E419ADA0089E3F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7D5832A01F4E93312EEF22A8 /* Pods_AtitudeTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D45502E419ADA0089E3F6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B2878B5CF922CCBCB77D0A27 /* Pods_Atitude_AtitudeUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1D25CA509CB666049C67BA54 /* Pods */ = {
			isa = PBXGroup;
			children = (
				9041EE1287B798145387B777 /* Pods-Atitude.debug.xcconfig */,
				43FE7A962C45A6A03416FCED /* Pods-Atitude.release.xcconfig */,
				17AD4D34635C9ECFEC808E3D /* Pods-Atitude-AtitudeUITests.debug.xcconfig */,
				7C43010EF285398031C2701C /* Pods-Atitude-AtitudeUITests.release.xcconfig */,
				6C95E9CFF7F2220B5FC3C69D /* Pods-AtitudeTests.debug.xcconfig */,
				CF907DEDD6B442BD4E451A56 /* Pods-AtitudeTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		A52D45252E419AD80089E3F6 = {
			isa = PBXGroup;
			children = (
				A52D45302E419AD80089E3F6 /* Atitude */,
				A52D454C2E419ADA0089E3F6 /* AtitudeTests */,
				A52D45562E419ADA0089E3F6 /* AtitudeUITests */,
				A52D452F2E419AD80089E3F6 /* Products */,
				1D25CA509CB666049C67BA54 /* Pods */,
				D6078C11BCB15D0E1C5EEFF3 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A52D452F2E419AD80089E3F6 /* Products */ = {
			isa = PBXGroup;
			children = (
				A52D452E2E419AD80089E3F6 /* Atitude.app */,
				A52D45492E419ADA0089E3F6 /* AtitudeTests.xctest */,
				A52D45532E419ADA0089E3F6 /* AtitudeUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D6078C11BCB15D0E1C5EEFF3 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2E1A128B442B729F5329C897 /* Pods_Atitude.framework */,
				ACF30694A05797A8FD4BE7BB /* Pods_Atitude_AtitudeUITests.framework */,
				8FE591ADC19E4A6D37078825 /* Pods_AtitudeTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A52D452D2E419AD80089E3F6 /* Atitude */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A52D455C2E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "Atitude" */;
			buildPhases = (
				C42AA607CAF690E0B10986D3 /* [CP] Check Pods Manifest.lock */,
				A52D452A2E419AD80089E3F6 /* Sources */,
				A52D452B2E419AD80089E3F6 /* Frameworks */,
				A52D452C2E419AD80089E3F6 /* Resources */,
				D725B06FE91C9E22A24841DB /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				A52D45302E419AD80089E3F6 /* Atitude */,
			);
			name = Atitude;
			productName = Atitude;
			productReference = A52D452E2E419AD80089E3F6 /* Atitude.app */;
			productType = "com.apple.product-type.application";
		};
		A52D45482E419ADA0089E3F6 /* AtitudeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A52D45612E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "AtitudeTests" */;
			buildPhases = (
				0BA9C53EACB3B2DC0F1FC9DF /* [CP] Check Pods Manifest.lock */,
				A52D45452E419ADA0089E3F6 /* Sources */,
				A52D45462E419ADA0089E3F6 /* Frameworks */,
				A52D45472E419ADA0089E3F6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A52D454B2E419ADA0089E3F6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A52D454C2E419ADA0089E3F6 /* AtitudeTests */,
			);
			name = AtitudeTests;
			productName = AtitudeTests;
			productReference = A52D45492E419ADA0089E3F6 /* AtitudeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A52D45522E419ADA0089E3F6 /* AtitudeUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A52D45642E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "AtitudeUITests" */;
			buildPhases = (
				5910D52462DEEC39C3A5F663 /* [CP] Check Pods Manifest.lock */,
				A52D454F2E419ADA0089E3F6 /* Sources */,
				A52D45502E419ADA0089E3F6 /* Frameworks */,
				A52D45512E419ADA0089E3F6 /* Resources */,
				6871B50B293FF39E3E1C753D /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				A52D45552E419ADA0089E3F6 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A52D45562E419ADA0089E3F6 /* AtitudeUITests */,
			);
			name = AtitudeUITests;
			productName = AtitudeUITests;
			productReference = A52D45532E419ADA0089E3F6 /* AtitudeUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A52D45262E419AD80089E3F6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					A52D452D2E419AD80089E3F6 = {
						CreatedOnToolsVersion = 16.4;
					};
					A52D45482E419ADA0089E3F6 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = A52D452D2E419AD80089E3F6;
					};
					A52D45522E419ADA0089E3F6 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = A52D452D2E419AD80089E3F6;
					};
				};
			};
			buildConfigurationList = A52D45292E419AD80089E3F6 /* Build configuration list for PBXProject "Atitude" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A52D45252E419AD80089E3F6;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = A52D452F2E419AD80089E3F6 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A52D452D2E419AD80089E3F6 /* Atitude */,
				A52D45482E419ADA0089E3F6 /* AtitudeTests */,
				A52D45522E419ADA0089E3F6 /* AtitudeUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A52D452C2E419AD80089E3F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D45472E419ADA0089E3F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D45512E419ADA0089E3F6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0BA9C53EACB3B2DC0F1FC9DF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AtitudeTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5910D52462DEEC39C3A5F663 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Atitude-AtitudeUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		6871B50B293FF39E3E1C753D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Atitude-AtitudeUITests/Pods-Atitude-AtitudeUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Atitude-AtitudeUITests/Pods-Atitude-AtitudeUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Atitude-AtitudeUITests/Pods-Atitude-AtitudeUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C42AA607CAF690E0B10986D3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Atitude-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D725B06FE91C9E22A24841DB /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Atitude/Pods-Atitude-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Atitude/Pods-Atitude-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Atitude/Pods-Atitude-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A52D452A2E419AD80089E3F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D45452E419ADA0089E3F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A52D454F2E419ADA0089E3F6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A52D454B2E419ADA0089E3F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A52D452D2E419AD80089E3F6 /* Atitude */;
			targetProxy = A52D454A2E419ADA0089E3F6 /* PBXContainerItemProxy */;
		};
		A52D45552E419ADA0089E3F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A52D452D2E419AD80089E3F6 /* Atitude */;
			targetProxy = A52D45542E419ADA0089E3F6 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A52D455D2E419ADA0089E3F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9041EE1287B798145387B777 /* Pods-Atitude.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Atitude/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.Atitude;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A52D455E2E419ADA0089E3F6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 43FE7A962C45A6A03416FCED /* Pods-Atitude.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Atitude/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.Atitude;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A52D455F2E419ADA0089E3F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = W83W7DVU38;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		A52D45602E419ADA0089E3F6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = W83W7DVU38;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A52D45622E419ADA0089E3F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6C95E9CFF7F2220B5FC3C69D /* Pods-AtitudeTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.AtitudeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Atitude.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Atitude";
			};
			name = Debug;
		};
		A52D45632E419ADA0089E3F6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CF907DEDD6B442BD4E451A56 /* Pods-AtitudeTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.AtitudeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Atitude.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Atitude";
			};
			name = Release;
		};
		A52D45652E419ADA0089E3F6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 17AD4D34635C9ECFEC808E3D /* Pods-Atitude-AtitudeUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.AtitudeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Atitude;
			};
			name = Debug;
		};
		A52D45662E419ADA0089E3F6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7C43010EF285398031C2701C /* Pods-Atitude-AtitudeUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.AtitudeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Atitude;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A52D45292E419AD80089E3F6 /* Build configuration list for PBXProject "Atitude" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A52D455F2E419ADA0089E3F6 /* Debug */,
				A52D45602E419ADA0089E3F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A52D455C2E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "Atitude" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A52D455D2E419ADA0089E3F6 /* Debug */,
				A52D455E2E419ADA0089E3F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A52D45612E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "AtitudeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A52D45622E419ADA0089E3F6 /* Debug */,
				A52D45632E419ADA0089E3F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A52D45642E419ADA0089E3F6 /* Build configuration list for PBXNativeTarget "AtitudeUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A52D45652E419ADA0089E3F6 /* Debug */,
				A52D45662E419ADA0089E3F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A52D45262E419AD80089E3F6 /* Project object */;
}
