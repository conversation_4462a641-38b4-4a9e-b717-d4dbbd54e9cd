//
//  AltitudeDataManager.h
//  Atitude
//
//  Created by fs0011 on 2025/8/5.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AltitudeDataManager : NSObject

+ (instancetype)sharedManager;

// 根据海拔计算气压 (kPa)
- (double)calculatePressureWithAltitude:(double)altitude;

// 根据海拔计算含氧量百分比
- (double)calculateOxygenPercentageWithAltitude:(double)altitude;

// 根据经纬度计算日出时间
- (NSDate *)calculateSunriseWithLatitude:(double)latitude longitude:(double)longitude date:(NSDate *)date;

// 根据经纬度计算日落时间
- (NSDate *)calculateSunsetWithLatitude:(double)latitude longitude:(double)longitude date:(NSDate *)date;

// 格式化时间显示
- (NSString *)formatTimeFromDate:(NSDate *)date;

// 根据坐标获取城市名称
- (void)getCityNameWithLatitude:(double)latitude longitude:(double)longitude completion:(void(^)(NSString *cityName))completion;

@end

NS_ASSUME_NONNULL_END
