//
//  zyeExtension.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/3.
//

import Foundation
import UIKit

extension UIView {
    // Origin
    var origin: CGPoint {
        get {
            return self.frame.origin
        }
        set {
            var frame = self.frame
            frame.origin = newValue
            self.frame = frame
        }
    }
    
    // Size
    var size: CGSize {
        get {
            return self.frame.size
        }
        set {
            var frame = self.frame
            frame.size = newValue
            self.frame = frame
        }
    }
    
    // X
    var x: CGFloat {
        get {
            return self.frame.origin.x
        }
        set {
            var frame = self.frame
            frame.origin.x = newValue
            self.frame = frame
        }
    }
    
    // Y
    var y: CGFloat {
        get {
            return self.frame.origin.y
        }
        set {
            var frame = self.frame
            frame.origin.y = newValue
            self.frame = frame
        }
    }
    
    // Width
    var width: CGFloat {
        get {
            return self.frame.size.width
        }
        set {
            var frame = self.frame
            frame.size.width = newValue
            self.frame = frame
        }
    }
    
    // Height
    var height: CGFloat {
        get {
            return self.frame.size.height
        }
        set {
            var frame = self.frame
            frame.size.height = newValue
            self.frame = frame
        }
    }
    
    // Bottom
    var bottom: CGFloat {
        get {
            return self.frame.origin.y + self.frame.size.height
        }
        set {
            var frame = self.frame
            frame.origin.y = newValue - self.frame.size.height
            self.frame = frame
        }
    }
    
    // Right
    var right: CGFloat {
        get {
            return self.frame.origin.x + self.frame.size.width
        }
        set {
            var frame = self.frame
            frame.origin.x = newValue - self.frame.size.width
            self.frame = frame
        }
    }
    
    // CenterX
    var centerX: CGFloat {
        get {
            return self.center.x
        }
        set {
            var center = self.center
            center.x = newValue
            self.center = center
        }
    }
    
    // CenterY
    var centerY: CGFloat {
        get {
            return self.center.y
        }
        set {
            var center = self.center
            center.y = newValue
            self.center = center
        }
    }
    
    // IgnoreEvent
    private struct AssociatedKeys {
        static var ignoreEvent = "ignoreEvent"
    }
    
    var ignoreEvent: Bool {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.ignoreEvent) as? Bool ?? false
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.ignoreEvent, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // Anti Double Click
    func antiDoubleClick() {
        self.ignoreEvent = true
        perform(#selector(setIgnoreEventFalse), with: nil, afterDelay: 0.5)
    }
    
    @objc private func setIgnoreEventFalse() {
        self.ignoreEvent = false
    }
}


extension Array where Element: Equatable {
    mutating func removeFirst(_ value: Element) {
        if let index = firstIndex(of: value) {
            remove(at: index)
        }
    }
}


