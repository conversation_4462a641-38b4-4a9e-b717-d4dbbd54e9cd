//
//  CreatUI.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/2.
//

import Foundation
import UIKit

extension UILabel {

    static func createLabel(title: String, textColor: UIColor, textAlignment: NSTextAlignment, font: UIFont) -> UILabel {
        let label = UILabel()
        label.font = font
        label.text = local(title)
        label.textColor = textColor
        label.textAlignment = textAlignment
        return label
    }
    
    static func createTopLeftLabel(title: String, textColor: UIColor, textAlignment: NSTextAlignment, font: UIFont) -> UILabel {
        let label = UILabel()
        label.font = font
        label.text = title
        label.textColor = textColor
        label.textAlignment = textAlignment
        return label
    }

    func changeAlignmentRightAndLeft() {
        guard let text = self.text, !text.isEmpty else { return }

        let textSize = (text as NSString).boundingRect(
            with: CGSize(width: self.frame.size.width, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .truncatesLastVisibleLine, .usesFontLeading],
            attributes: [NSAttributedString.Key.font: self.font],
            context: nil
        )
        
        let margin = (self.frame.size.width - textSize.width) / CGFloat(text.count - 1)
        
        let attributedString = NSMutableAttributedString(string: text)
        attributedString.addAttribute(.kern, value: margin, range: NSRange(location: 0, length: text.count - 1))
        self.attributedText = attributedString
    }
}

extension UIButton {

    static func createButton(withImageName name: String?) -> UIButton {
        let btn: UIButton
        if let name = name, !name.isEmpty {
            btn = createButton(normalImageName: name, normalTitle: nil, normalColor: nil, selectedName: nil, selectedTitle: nil, selectedColor: nil, font: nil)
        } else {
            btn = createButton(normalImageName: nil, normalTitle: nil, normalColor: nil, selectedName: nil, selectedTitle: nil, selectedColor: nil, font: nil)
        }
        btn.sizeToFit()
        return btn
    }

    static func createButton(normalImageName: String?, selectedName: String?) -> UIButton {
        let btn = createButton(normalImageName: normalImageName, normalTitle: nil, normalColor: nil, selectedName: selectedName, selectedTitle: nil, selectedColor: nil, font: nil)
        btn.sizeToFit()
        return btn
    }

    static func createButton(title: String, color: UIColor, font: UIFont) -> UIButton {
        let btn = createButton(normalImageName: nil, normalTitle: title, normalColor: color, selectedName: nil, selectedTitle: nil, selectedColor: nil, font: font)
        return btn
    }

    static func createButton(normalTitle: String, normalColor: UIColor, selectedTitle: String?, selectedColor: UIColor?, font: UIFont) -> UIButton {
        let btn = createButton(normalImageName: nil, normalTitle: normalTitle, normalColor: normalColor, selectedName: nil, selectedTitle: selectedTitle, selectedColor: selectedColor, font: font)
        return btn
    }

    static func createButton(title: String, color: UIColor, font: UIFont, imageName: String) -> UIButton {
        let btn = createButton(normalImageName: imageName, normalTitle: title, normalColor: color, selectedName: nil, selectedTitle: nil, selectedColor: nil, font: font)
        return btn
    }

    static func createButton(normalImageName: String?, normalTitle: String?, normalColor: UIColor?, selectedName: String?, selectedTitle: String?, selectedColor: UIColor?, font: UIFont?) -> UIButton {
        let btn = UIButton(type: .custom)
        if let normalImageName = normalImageName {
            btn.setImage(UIImage(named: normalImageName), for: .normal)
        }
        if let normalTitle = normalTitle {
            btn.setTitle(local(normalTitle), for: .normal)
        }
        if let normalColor = normalColor {
            btn.setTitleColor(normalColor, for: .normal)
        }
        if let selectedColor = selectedColor {
            btn.setTitleColor(selectedColor, for: .selected)
        }
        if let selectedName = selectedName {
            btn.setImage(UIImage(named: selectedName), for: .selected)
        }
        if let selectedTitle = selectedTitle {
            btn.setTitle(local(selectedTitle), for: .selected)
        }
        if let font = font {
            btn.titleLabel?.font = font
        }
        btn.sizeToFit()
        return btn
    }

    static func createButton(withBGImageName name: String) -> UIButton {
        let btn = UIButton(type: .custom)
        btn.setBackgroundImage(UIImage(named: name), for: .normal)
        return btn
    }
}


extension UIImageView {
    
    static func createRoundImageView(name: String, width: CGFloat) -> UIImageView {
        let imageView = UIImageView()
        imageView.image = UIImage(named: name)
        imageView.frame.size = CGSize(width: width, height: width)
        imageView.layer.cornerRadius = width / 2
        imageView.layer.masksToBounds = true
        return imageView
    }

    static func createSizeFitImageView(name: String) -> UIImageView {
        let imageView = UIImageView()
        if !name.isEmpty {
            imageView.image = UIImage(named: name)
            imageView.sizeToFit()
        }
        return imageView
    }
}


extension UIColor {
    /**
     16进制颜色转换为UIColor

     - parameter hexColor: 16进制字符串（可以以0x开头，可以以#开头，也可以就是6位的16进制）
     - parameter opacity: 透明度
     - returns: 16进制字符串对应的颜色
     */
    class func colorWithHexString(_ hexColor: String, alpha: CGFloat) -> UIColor {
        var cString = hexColor.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()
        
        // String should be 6 or 8 characters
        if cString.count < 6 { return UIColor.black }
        
        // strip 0X if it appears
        if cString.hasPrefix("0X") { cString.removeFirst(2) }
        if cString.hasPrefix("#") { cString.removeFirst(1) }
        
        if cString.count != 6 { return UIColor.black }
        
        // Separate into r, g, b substrings
        var range = NSRange(location: 0, length: 2)
        let rString = (cString as NSString).substring(with: range)
        
        range.location = 2
        let gString = (cString as NSString).substring(with: range)
        
        range.location = 4
        let bString = (cString as NSString).substring(with: range)
        
        // Scan values
        var r: UInt64 = 0, g: UInt64 = 0, b: UInt64 = 0
        Scanner(string: rString).scanHexInt64(&r)
        Scanner(string: gString).scanHexInt64(&g)
        Scanner(string: bString).scanHexInt64(&b)
        
        return UIColor(red: CGFloat(r) / 255.0, green: CGFloat(g) / 255.0, blue: CGFloat(b) / 255.0, alpha: alpha)
    }
    
    class func hexStringFromColor(_ color: UIColor) -> String {
        guard let components = color.cgColor.components, components.count >= 3 else {
            return "#000000" // Default to black if components are unavailable
        }
        
        let r = components[0]
        let g = components[1]
        let b = components[2]
        
        return String(format: "#%02lX%02lX%02lX", lroundf(Float(r * 255)), lroundf(Float(g * 255)), lroundf(Float(b * 255)))
    }
}

extension UITabBarItem{
    public  convenience init(itemName:String) {
    
        self.init()
        self.title = local(itemName)
        self.image = UIImage(named: "\(itemName)-未选中")?.withRenderingMode(.alwaysOriginal)
        self.titlePositionAdjustment = UIOffset(horizontal: 0, vertical: -5)
        self.selectedImage = UIImage(named: "\(itemName)-选中")?.withRenderingMode(.alwaysOriginal)
        self.imageInsets = UIEdgeInsets(top: 8, left: 0, bottom: 0, right: 0)
        self.setTitleTextAttributes([NSAttributedString.Key.font:UIFont.systemFont(ofSize: 10, weight: .semibold),
                                     NSAttributedString.Key.foregroundColor:unseletColor], for: .normal)
        self.setTitleTextAttributes([NSAttributedString.Key.font:UIFont.systemFont(ofSize: 10, weight: .semibold),
                                     NSAttributedString.Key.foregroundColor:seletedColor], for: .selected)
        
    }
    
}
