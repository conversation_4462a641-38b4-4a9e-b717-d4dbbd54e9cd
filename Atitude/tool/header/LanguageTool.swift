//
//  LanguageTool.swift
//  Atitude
//
//  Created by fs0011 on 2025/8/5.
//

import Foundation

enum Language {
    case chineseSimplified
    case chineseTranditional
    case english
    case other
}

class LanguageTool {
    static func currentLanguage() -> Language {
        guard let languageCode = Locale.current.languageCode else {
            return .other
        }
        
        switch languageCode {
        case "zh":
            if let regionCode = Locale.current.regionCode {
                switch regionCode {
                case "CN", "SG":
                    return .chineseSimplified
                case "TW", "HK", "MO":
                    return .chineseTranditional
                default:
                    return .chineseSimplified
                }
            }
            return .chineseSimplified
        case "en":
            return .english
        default:
            return .other
        }
    }
}
