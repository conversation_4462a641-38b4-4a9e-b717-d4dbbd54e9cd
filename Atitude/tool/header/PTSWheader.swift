//
//  STheader.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/2.
//

import Foundation
import UIKit

var SCREEN_WIDTH: CGFloat {
    if #available(iOS 13.0, *) {
        if let orientation = UIApplication.shared.windows.first?.windowScene?.interfaceOrientation {
            let w = UIScreen.main.bounds.width>UIScreen.main.bounds.height ? UIScreen.main.bounds.height : UIScreen.main.bounds.width
            let h = UIScreen.main.bounds.width>UIScreen.main.bounds.height ? UIScreen.main.bounds.width : UIScreen.main.bounds.height
            let i = (orientation.isLandscape ? h : w)
            print("\(orientation.isLandscape)i==\(i)")
            return i
        }
    } else {
        // Fallback on earlier versions
    }
    return UIScreen.main.bounds.width
}

var SCREEN_HEIGHT: CGFloat {
    if #available(iOS 13.0, *) {
        if let orientation = UIApplication.shared.windows.first?.windowScene?.interfaceOrientation {
            
            let w = UIScreen.main.bounds.width>UIScreen.main.bounds.height ? UIScreen.main.bounds.height : UIScreen.main.bounds.width
            let h = UIScreen.main.bounds.width>UIScreen.main.bounds.height ? UIScreen.main.bounds.width : UIScreen.main.bounds.height
            let i = (orientation.isLandscape ? w : h)
            
            return i
        }
    } else {
        // Fallback on earlier versions
    }
    return UIScreen.main.bounds.height
}

let darkTextColor = UIColor.colorWithHexString("333333", alpha: 1)
let themColor = UIColor.colorWithHexString("#FFEB53", alpha: 1)
let seletedColor = UIColor.colorWithHexString("#D9D9D9", alpha: 1)
let unseletColor = UIColor.colorWithHexString("FFF8C5", alpha: 1)



let titleMedlumFont = UIFont.systemFont(ofSize: 18 * scaleX, weight: .medium)
let bigMedlumFont = UIFont.systemFont(ofSize: 16 * scaleX, weight: .medium)
let standMedlumFont = UIFont.systemFont(ofSize: 14 * scaleX, weight: .medium)
let smallMedlumFont = UIFont.systemFont(ofSize: 12 * scaleX, weight: .medium)

let standFont = UIFont.systemFont(ofSize: 14 * scaleX)
let smallFont = UIFont.systemFont(ofSize: 12 * scaleX)
let boldFont = UIFont.systemFont(ofSize: 18 * scaleX, weight: .semibold)
let midBoldFont = UIFont.systemFont(ofSize: 16 * scaleX, weight: .semibold)

let standSpace = 16*scaleX
let bigSpace = 20*scaleX

let yyxyUrl = "https://www.freeprivacypolicy.com/live/************************************"
let yyzcUrl = "https://www.freeprivacypolicy.com/live/************************************"


let AD_APP_ID  = "5466856"
let AD_SplashAd = "888837426"
let AD_Banner = "955433812"
let AD_Fullscreen = "955433812"
let updateJsonUrl = "https://controls.oss-cn-hangzhou.aliyuncs.com/ios-moblile-transfer_2.4.1.json"

let startWithADTimes = "startWithADTimes"

let isChinese = LanguageTool.currentLanguage() == .chineseSimplified || LanguageTool.currentLanguage() == .chineseTranditional


var scaleX: CGFloat {
    let screenWidth = UIScreen.main.bounds.width
    let screenHeight = UIScreen.main.bounds.height
    
    let scale = (screenWidth < screenHeight ? screenWidth / 375.0 : screenHeight / 375.0)
    return scale > 1.7 ? 1.7 : scale
}





var NOChangeScaleX: CGFloat {
    let screenWidth = UIScreen.main.bounds.width
    let screenHeight = UIScreen.main.bounds.height
    
    let scale = (screenWidth < screenHeight ? screenWidth / 375.0 : screenHeight / 375.0)
    return  scale
}

func local(_ key: String) -> String {
    return NSLocalizedString(key, comment: "description for this key.")
}
func localizedFormatString(with key: String, value: CVarArg) -> String {
    return String(format: local(key), value)
}


/**
 永久存储对象
 - Parameters:
    - object: 需存储的对象
    - key: 对应的 key
 */
func persistentSetObject(_ object: Any, forKey key: String) {
    let defaults = UserDefaults.standard
    defaults.set(object, forKey: key)
    defaults.synchronize()
}

/**
 取出永久存储的对象
 - Parameter key: 所需对象对应的 key
 - Returns: key 所对应的对象
 */
func persistentGetObject(forKey key: String) -> Any? {
    return UserDefaults.standard.object(forKey: key)
}

/**
 清除 UserDefaults 保存的所有数据
 */
func persistentRemoveAllData() {
    if let bundleIdentifier = Bundle.main.bundleIdentifier {
        UserDefaults.standard.removePersistentDomain(forName: bundleIdentifier)
    }
}
func isChineseEnvironment() -> Bool {
    if let languageCode = Locale.current.languageCode {
        return languageCode == "zh"
    }
    return false
}
/**
 清除 UserDefaults 保存的指定数据
 - Parameter key: 所需删除对象对应的 key
 */
func persistentRemove(forKey key: String) {
    let defaults = UserDefaults.standard
    defaults.removeObject(forKey: key)
    defaults.synchronize()
}

func createImageWithColor(color:UIColor)->UIImage
{
    let rect = CGRect(x: 0, y: 0, width: 1, height: 1)
            UIGraphicsBeginImageContext(rect.size)
            
            if let context = UIGraphicsGetCurrentContext() {
                context.setFillColor(color.cgColor)
                context.fill(rect)
            }
            
            let image = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            return image ?? UIImage()
}

func hasNotch() -> Bool {
    if let window = UIApplication.shared.windows.first {
        let top = window.safeAreaInsets.top
        return top > 20
    }
    return false
}

// 获取自定义导航栏高度
func getNavigationBarHeight() -> CGFloat {
    return hasNotch() ? 88.0 : 64.0
}


func getStatusBarHeight() -> CGFloat {
    return hasNotch() ? 44.0 : 20.0
}

func KDNavH() -> CGFloat {
    return hasNotch() ? 20.0 : 0
}

let bottomHeight = (hasNotch() ? 20 : 0)
 //额外的高度
