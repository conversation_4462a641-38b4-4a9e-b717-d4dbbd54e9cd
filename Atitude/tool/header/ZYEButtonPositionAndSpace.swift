//
//  ZYEButtonPositionAndSpace.swift
//  phonetransSW
//
//  Created by fs0011 on 2024/7/8.
//

import Foundation
import UIKit

enum SSImagePositionType: Int {
    case left   //图片在左，标题在右，默认风格
    case right  //图片在右，标题在左
    case top    //图片在上，标题在下
    case bottom //图片在下，标题在上
}

enum SSEdgeInsetsType: Int {
    case title //标题
    case image //图片
}

enum SSMarginType: Int {
    case top
    case bottom
    case left
    case right
    case topLeft
    case topRight
    case bottomLeft
    case bottomRight
}

extension UIButton {
    /**
     *  利用UIButton的titleEdgeInsets和imageEdgeInsets来实现图片和标题的自由排布
     *  注意：1.该方法需在设置图片和标题之后才调用;
     *         2.图片和标题改变后需再次调用以重新计算titleEdgeInsets和imageEdgeInsets
     *
     *  @param type    图片位置类型
     *  @param spacing 图片和标题之间的间隙
     */
    func setImagePosition(with type: SSImagePositionType, spacing: CGFloat) {
        let spacing = spacing - 1
        guard let imageSize = image(for: .normal)?.size,
              let title = title(for: .normal),
              let titleLabel = titleLabel else { return }
        
        var titleSize = size(for: title, font: titleLabel.font, size: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude), mode: titleLabel.lineBreakMode)
        
        if titleLabel.adjustsFontSizeToFitWidth && (type == .left || type == .right) {
            titleLabel.baselineAdjustment = .alignCenters
        }
        
        switch type {
        case .left:
            titleEdgeInsets = UIEdgeInsets(top: 0, left: spacing, bottom: 0, right: 0)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: spacing)
            
        case .right:
            if frame != .zero {
                let titleMaxHeight: CGFloat
                let lineBreakMode = titleLabel.lineBreakMode
                if lineBreakMode == .byWordWrapping || lineBreakMode == .byCharWrapping {
                    titleMaxHeight = CGFloat.greatestFiniteMagnitude
                } else {
                    titleMaxHeight = titleLabel.font.pointSize
                }
                
                let titleMaxSize = CGSize(width: frame.width - (imageSize.width + spacing), height: titleMaxHeight)
                titleSize = size(for: title, font: titleLabel.font, size: titleMaxSize, mode: lineBreakMode)
            }
            
            titleEdgeInsets = UIEdgeInsets(top: 0, left: -imageSize.width, bottom: 0, right: imageSize.width + spacing)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: titleSize.width + spacing, bottom: 0, right: -titleSize.width)
            
        case .top:
            titleEdgeInsets = UIEdgeInsets(top: 0, left: -imageSize.width, bottom: -(imageSize.height + spacing), right: 0)
            imageEdgeInsets = UIEdgeInsets(top: -(titleSize.height + spacing), left: 0, bottom: 0, right: -titleSize.width)
            
        case .bottom:
            titleEdgeInsets = UIEdgeInsets(top: -(imageSize.height + spacing), left: -imageSize.width, bottom: 0, right: 0)
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: -(titleSize.height + spacing), right: -titleSize.width)
        }
    }
    
    /**
     *  按钮只设置了title or image，该方法可以改变它们的位置
     *
     *  @param edgeInsetsType <#edgeInsetsType description#>
     *  @param marginType     <#marginType description#>
     *  @param margin         <#margin description#>
     */
    func setEdgeInsets(with edgeInsetsType: SSEdgeInsetsType, marginType: SSMarginType, margin: CGFloat) {
        var itemSize = CGSize.zero
        if edgeInsetsType == .title {
            if let title = title(for: .normal), let font = titleLabel?.font {
                itemSize = size(for: title, font: font, size: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude), mode: titleLabel?.lineBreakMode ?? .byWordWrapping)
            }
        } else {
            itemSize = image(for: .normal)?.size ?? CGSize.zero
        }
        
        let horizontalDelta = (frame.width - itemSize.width) / 2 - margin
        let verticalDelta = (frame.height - itemSize.height) / 2 - margin
        
        var horizontalSignFlag = 1
        var verticalSignFlag = 1
        
        switch marginType {
        case .top:
            horizontalSignFlag = 0
            verticalSignFlag = -1
        case .bottom:
            horizontalSignFlag = 0
            verticalSignFlag = 1
        case .left:
            horizontalSignFlag = -1
            verticalSignFlag = 0
        case .right:
            horizontalSignFlag = 1
            verticalSignFlag = 0
        case .topLeft:
            horizontalSignFlag = -1
            verticalSignFlag = -1
        case .topRight:
            horizontalSignFlag = 1
            verticalSignFlag = -1
        case .bottomLeft:
            horizontalSignFlag = -1
            verticalSignFlag = 1
        case .bottomRight:
            horizontalSignFlag = 1
            verticalSignFlag = 1
        }
        
        let edgeInsets = UIEdgeInsets(top: verticalDelta * CGFloat(verticalSignFlag), left: horizontalDelta * CGFloat(horizontalSignFlag), bottom: -verticalDelta * CGFloat(verticalSignFlag), right: -horizontalDelta * CGFloat(horizontalSignFlag))
        
        if edgeInsetsType == .title {
            titleEdgeInsets = edgeInsets
        } else {
            imageEdgeInsets = edgeInsets
        }
    }
    
    /**
     *  图片在上，标题在下
     *
     *  @param spacing image 和 title 之间的间隙
     */
    @available(*, deprecated, message: "Method deprecated. Use `setImagePosition(with:spacing:)`")
    func setImageUpTitleDown(with spacing: CGFloat) {
        setImagePosition(with: .top, spacing: spacing)
    }
    
    /**
     *  图片在右，标题在左
     *
     *  @param spacing image 和 title 之间的间隙
     */
    @available(*, deprecated, message: "Method deprecated. Use `setImagePosition(with:spacing:)`")
    func setImageRightTitleLeft(with spacing: CGFloat) {
        setImagePosition(with: .right, spacing: spacing)
    }
    
    // Helper method for calculating size of text
    private func size(for text: String, font: UIFont, size: CGSize, mode: NSLineBreakMode) -> CGSize {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineBreakMode = mode
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .paragraphStyle: paragraphStyle
        ]
        
        let rect = text.boundingRect(with: size, options: [.usesLineFragmentOrigin, .usesFontLeading], attributes: attributes, context: nil)
        return rect.size
    }
}
