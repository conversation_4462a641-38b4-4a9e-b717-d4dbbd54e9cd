//
//  STSwftObj.swift
//  SearchT
//
//  Created by fs0011 on 2024/7/3.
//

import Foundation
typealias ActionCallBack = () -> Void
typealias ArgumentCallBack = (Any) -> Void
extension NSObject {
    // 定义两个关联对象键
    private struct AssociatedKeys {
        static var actionCallBack = "actionCallBack"
        static var argumentCallBack = "argumentCallBack"
    }
    
    // 定义无参数的闭包属性
    var actionCallBack: ActionCallBack? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.actionCallBack) as? ActionCallBack
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.actionCallBack, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // 定义带参数的闭包属性
    var argumentCallBack: ArgumentCallBack? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.argumentCallBack) as? ArgumentCallBack
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.argumentCallBack, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // 方法：设置无参数的闭包
    func setActionCallBack(_ callBack: @escaping ActionCallBack) {
        actionCallBack = callBack
    }
    
    // 方法：设置带参数的闭包
    func setArgumentCallBack(_ callBack: @escaping ArgumentCallBack) {
        argumentCallBack = callBack
    }
    
    // 方法：调用无参数的闭包
    func executeActionCallBack() {
        actionCallBack?()
    }
    
    // 方法：调用带参数的闭包
    func executeArgumentCallBack(sender: Any) {
        argumentCallBack?(sender)
    }
}
