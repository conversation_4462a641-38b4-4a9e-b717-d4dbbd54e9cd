//
//  AltitudeDataManager.m
//  Atitude
//
//  Created by fs0011 on 2025/8/5.
//

#import "AltitudeDataManager.h"
#import <math.h>

@implementation AltitudeDataManager

+ (instancetype)sharedManager {
    static AltitudeDataManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[AltitudeDataManager alloc] init];
    });
    return manager;
}

// 根据海拔计算气压 (使用标准大气压公式)
- (double)calculatePressureWithAltitude:(double)altitude {
    // 标准大气压公式: P = P0 * (1 - 0.0065 * h / T0)^(g * M / (R * 0.0065))
    // P0 = 101.325 kPa (海平面标准大气压)
    // h = 海拔高度 (m)
    // T0 = 288.15 K (海平面标准温度)
    // g = 9.80665 m/s² (重力加速度)
    // M = 0.0289644 kg/mol (空气摩尔质量)
    // R = 8.31432 J/(mol·K) (通用气体常数)
    
    double P0 = 101.325; // kPa
    double T0 = 288.15;  // K
    double g = 9.80665;  // m/s²
    double M = 0.0289644; // kg/mol
    double R = 8.31432;  // J/(mol·K)
    double L = 0.0065;   // K/m
    
    double exponent = (g * M) / (R * L);
    double pressure = P0 * pow(1 - (L * altitude / T0), exponent);
    
    return pressure;
}

// 根据海拔计算含氧量百分比
- (double)calculateOxygenPercentageWithAltitude:(double)altitude {
    // 氧气浓度随海拔变化的近似公式
    // 海平面氧气浓度约为20.9%
    // 每升高1000米，氧气浓度下降约3%
    
    double seaLevelOxygen = 20.9; // 海平面氧气浓度 %
    double altitudeKm = altitude / 1000.0; // 转换为公里
    
    // 使用指数衰减模型
    double oxygenPercentage = seaLevelOxygen * exp(-altitudeKm * 0.12);
    
    return oxygenPercentage;
}

// 根据经纬度计算日出时间
- (NSDate *)calculateSunriseWithLatitude:(double)latitude longitude:(double)longitude date:(NSDate *)date {
    return [self calculateSunEventWithLatitude:latitude longitude:longitude date:date isSunrise:YES];
}

// 根据经纬度计算日落时间
- (NSDate *)calculateSunsetWithLatitude:(double)latitude longitude:(double)longitude date:(NSDate *)date {
    return [self calculateSunEventWithLatitude:latitude longitude:longitude date:date isSunrise:NO];
}

// 计算日出日落的核心算法
- (NSDate *)calculateSunEventWithLatitude:(double)latitude longitude:(double)longitude date:(NSDate *)date isSunrise:(BOOL)isSunrise {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:date];
    
    // 计算一年中的第几天
    NSInteger dayOfYear = [calendar ordinalityOfUnit:NSCalendarUnitDay inUnit:NSCalendarUnitYear forDate:date];
    
    // 转换为弧度
    double latRad = latitude * M_PI / 180.0;
    
    // 计算太阳赤纬角
    double declination = 23.45 * sin((360.0 * (284 + dayOfYear) / 365.0) * M_PI / 180.0) * M_PI / 180.0;
    
    // 计算时角
    double hourAngle = acos(-tan(latRad) * tan(declination));
    
    // 计算日出日落时间 (UTC)
    double timeUTC;
    if (isSunrise) {
        timeUTC = 12.0 - (hourAngle * 180.0 / M_PI) / 15.0 - longitude / 15.0;
    } else {
        timeUTC = 12.0 + (hourAngle * 180.0 / M_PI) / 15.0 - longitude / 15.0;
    }
    
    // 转换为本地时间 (假设东八区)
    double localTime = timeUTC + 8.0;
    
    // 处理跨日情况
    if (localTime < 0) {
        localTime += 24;
    } else if (localTime >= 24) {
        localTime -= 24;
    }
    
    // 转换为NSDate
    NSInteger hours = (NSInteger)localTime;
    NSInteger minutes = (NSInteger)((localTime - hours) * 60);
    NSInteger seconds = (NSInteger)(((localTime - hours) * 60 - minutes) * 60);
    
    components.hour = hours;
    components.minute = minutes;
    components.second = seconds;
    
    return [calendar dateFromComponents:components];
}

// 格式化时间显示
- (NSString *)formatTimeFromDate:(NSDate *)date {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"HH:mm:ss";
    return [formatter stringFromDate:date];
}

// 根据坐标获取城市名称
- (void)getCityNameWithLatitude:(double)latitude longitude:(double)longitude completion:(void(^)(NSString *cityName))completion {
    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
    CLLocation *location = [[CLLocation alloc] initWithLatitude:latitude longitude:longitude];
    
    [geocoder reverseGeocodeLocation:location completionHandler:^(NSArray<CLPlacemark *> * _Nullable placemarks, NSError * _Nullable error) {
        if (error || placemarks.count == 0) {
            if (completion) {
                completion(@"未知位置");
            }
            return;
        }
        
        CLPlacemark *placemark = placemarks.firstObject;
        NSString *cityName = placemark.locality ?: placemark.administrativeArea ?: @"未知位置";
        
        if (completion) {
            completion(cityName);
        }
    }];
}

@end
