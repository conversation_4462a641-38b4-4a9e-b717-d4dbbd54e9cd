//
//  ViewController.m
//  Atitude
//
//  Created by fs0011 on 2025/8/5.
//

#import "ViewController.h"
#import "Atitude-Swift.h"
#import "AltitudeDataManager.h"

@interface ViewController ()

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupLocationManager];
    [self setupCompass];
    [self setDefaultValues];
}

- (void)setDefaultValues {
    // 设置默认值，避免空白显示
    self.altitudeLabel.text = @"--M";
    self.accuracyLabel.text = @"海拔精度:--M";
    self.locationLabel.text = @"获取位置中...";
    self.pressureLabel.text = @"--kPa";
    self.oxygenLabel.text = @"--%";
    self.sunriseLabel.text = @"--:--:--";
    self.sunsetLabel.text = @"--:--:--";
    self.longitudeLabel.text = @"--";
    self.latitudeLabel.text = @"--";

    // 设置当前日期
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyy年M月d日";
    self.dateLabel.text = [dateFormatter stringFromDate:[NSDate date]];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor colorWithHexString:@"F5F7FA" alpha:1.0];

    // 获取屏幕宽度，计算比例
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat scale = screenWidth / 375.0;

    [self setupTopButtons:scale];
    [self setupCompassView:scale];
    [self setupDataLabels:scale];
}

- (void)setupTopButtons:(CGFloat)scale {
    // 拍照按钮 - 左上角
    self.cameraButton = [UIButton createButton:@"拍照"];
    [self.cameraButton addTarget:self action:@selector(cameraButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.cameraButton];
    [self.cameraButton snp_makeConstraints:^(ConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20 * scale);
        make.top.equalTo(self.view.snp_safeAreaLayoutGuideTop).offset(20 * scale);
        make.width.equalTo(28 * scale);
        make.height.equalTo(20 * scale);
    }];

    // 我的足迹按钮 - 右上角
    self.trackButton = [UIButton createButton:@"我的足迹"];
    [self.trackButton addTarget:self action:@selector(trackButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.trackButton];
    [self.trackButton snp_makeConstraints:^(ConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-20 * scale);
        make.top.equalTo(self.view.snp_safeAreaLayoutGuideTop).offset(20 * scale);
        make.width.equalTo(24 * scale);
        make.height.equalTo(24 * scale);
    }];

    // 设置按钮 - 我的足迹左边
    self.settingsButton = [UIButton createButton:@"设置"];
    [self.settingsButton addTarget:self action:@selector(settingsButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.settingsButton];
    [self.settingsButton snp_makeConstraints:^(ConstraintMaker *make) {
        make.right.equalTo(self.trackButton.snp_left).offset(-20 * scale);
        make.top.equalTo(self.view.snp_safeAreaLayoutGuideTop).offset(20 * scale);
        make.width.equalTo(24 * scale);
        make.height.equalTo(24 * scale);
    }];
}

- (void)setupCompassView:(CGFloat)scale {
    // 表盘视图
    self.compassView = [UIImageView createSizeFitImageView:@"表盘"];
    [self.view addSubview:self.compassView];
    [self.compassView snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.cameraButton.snp_bottom).offset(40 * scale);
        make.width.height.equalTo(300 * scale);
    }];

    // 北指针
    self.northPointer = [UIImageView createSizeFitImageView:@"北针"];
    [self.view addSubview:self.northPointer];
    [self.northPointer snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.compassView);
        make.bottom.equalTo(self.compassView.snp_top).offset(6 * scale);
    }];

    // 南指针
    self.southPointer = [UIImageView createSizeFitImageView:@"南针"];
    [self.view addSubview:self.southPointer];
    [self.southPointer snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.compassView);
        make.top.equalTo(self.compassView.snp_bottom).offset(-6 * scale);
    }];

    // 海拔显示 - 表盘中心
    self.altitudeLabel = [UILabel createTopLeftLabel:@"5104M"
                                           textColor:[UIColor whiteColor]
                                       textAlignment:NSTextAlignmentCenter
                                                font:[UIFont boldSystemFontOfSize:48 * scale]];
    [self.view addSubview:self.altitudeLabel];
    [self.altitudeLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.compassView);
        make.centerY.equalTo(self.compassView).offset(-20 * scale);
    }];

    // 当前海拔标题
    UILabel *currentAltitudeTitle = [UILabel createTopLeftLabel:@"当前海拔"
                                                      textColor:[UIColor whiteColor]
                                                  textAlignment:NSTextAlignmentCenter
                                                           font:[UIFont systemFontOfSize:16 * scale]];
    [self.view addSubview:currentAltitudeTitle];
    [currentAltitudeTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.compassView);
        make.bottom.equalTo(self.altitudeLabel.snp_top).offset(-5 * scale);
    }];

    // 海拔精度
    self.accuracyLabel = [UILabel createTopLeftLabel:@"海拔精度:30M"
                                           textColor:[UIColor whiteColor]
                                       textAlignment:NSTextAlignmentCenter
                                                font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:self.accuracyLabel];
    [self.accuracyLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.compassView);
        make.top.equalTo(self.altitudeLabel.snp_bottom).offset(5 * scale);
    }];
}

- (void)setupDataLabels:(CGFloat)scale {
    // 位置信息
    UIImageView *locationIcon = [UIImageView createSizeFitImageView:@"定位"];
    [self.view addSubview:locationIcon];
    [locationIcon snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.view).offset(-40 * scale);
        make.top.equalTo(self.compassView.snp_bottom).offset(40 * scale);
        make.width.height.equalTo(16 * scale);
    }];

    self.locationLabel = [UILabel createTopLeftLabel:@"佛山市"
                                           textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                       textAlignment:NSTextAlignmentLeft
                                                font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.locationLabel];
    [self.locationLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.left.equalTo(locationIcon.snp_right).offset(8 * scale);
        make.centerY.equalTo(locationIcon);
    }];

    // 日期
    self.dateLabel = [UILabel createTopLeftLabel:@"2025年7月28日"
                                       textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                   textAlignment:NSTextAlignmentCenter
                                            font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:self.dateLabel];
    [self.dateLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.locationLabel.snp_bottom).offset(10 * scale);
    }];

    [self setupBottomDataLabels:scale];
}

- (void)setupBottomDataLabels:(CGFloat)scale {
    // 气压和含氧量
    UILabel *pressureTitle = [UILabel createTopLeftLabel:@"气压"
                                               textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                           textAlignment:NSTextAlignmentCenter
                                                    font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:pressureTitle];

    self.pressureLabel = [UILabel createTopLeftLabel:@"59.9kPa"
                                           textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                       textAlignment:NSTextAlignmentCenter
                                                font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.pressureLabel];

    UILabel *oxygenTitle = [UILabel createTopLeftLabel:@"含氧量"
                                             textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                         textAlignment:NSTextAlignmentCenter
                                                  font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:oxygenTitle];

    self.oxygenLabel = [UILabel createTopLeftLabel:@"20%"
                                         textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                     textAlignment:NSTextAlignmentCenter
                                              font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.oxygenLabel];

    // 日出日落
    UILabel *sunriseTitle = [UILabel createTopLeftLabel:@"日出"
                                              textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                          textAlignment:NSTextAlignmentCenter
                                                   font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:sunriseTitle];

    self.sunriseLabel = [UILabel createTopLeftLabel:@"05:57:32"
                                          textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                      textAlignment:NSTextAlignmentCenter
                                               font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.sunriseLabel];

    UILabel *sunsetTitle = [UILabel createTopLeftLabel:@"日落"
                                             textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                         textAlignment:NSTextAlignmentCenter
                                                  font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:sunsetTitle];

    self.sunsetLabel = [UILabel createTopLeftLabel:@"18:52:27"
                                         textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                     textAlignment:NSTextAlignmentCenter
                                              font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.sunsetLabel];

    // 经纬度
    UILabel *longitudeTitle = [UILabel createTopLeftLabel:@"经度"
                                                textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                            textAlignment:NSTextAlignmentCenter
                                                     font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:longitudeTitle];

    self.longitudeLabel = [UILabel createTopLeftLabel:@"113.0524"
                                            textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                        textAlignment:NSTextAlignmentCenter
                                                 font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.longitudeLabel];

    UILabel *latitudeTitle = [UILabel createTopLeftLabel:@"纬度"
                                               textColor:[UIColor colorWithHexString:@"666666" alpha:1.0]
                                           textAlignment:NSTextAlignmentCenter
                                                    font:[UIFont systemFontOfSize:14 * scale]];
    [self.view addSubview:latitudeTitle];

    self.latitudeLabel = [UILabel createTopLeftLabel:@"23.0131"
                                           textColor:[UIColor colorWithHexString:@"333333" alpha:1.0]
                                       textAlignment:NSTextAlignmentCenter
                                                font:[UIFont boldSystemFontOfSize:18 * scale]];
    [self.view addSubview:self.latitudeLabel];

    [self layoutBottomLabels:scale
               pressureTitle:pressureTitle
                oxygenTitle:oxygenTitle
               sunriseTitle:sunriseTitle
                sunsetTitle:sunsetTitle
             longitudeTitle:longitudeTitle
              latitudeTitle:latitudeTitle];
}

- (void)layoutBottomLabels:(CGFloat)scale
             pressureTitle:(UILabel *)pressureTitle
              oxygenTitle:(UILabel *)oxygenTitle
             sunriseTitle:(UILabel *)sunriseTitle
              sunsetTitle:(UILabel *)sunsetTitle
           longitudeTitle:(UILabel *)longitudeTitle
            latitudeTitle:(UILabel *)latitudeTitle {

    // 第一行：气压和含氧量
    [pressureTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.left.equalTo(self.view).offset(60 * scale);
        make.top.equalTo(self.dateLabel.snp_bottom).offset(40 * scale);
    }];

    [self.pressureLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(pressureTitle);
        make.top.equalTo(pressureTitle.snp_bottom).offset(8 * scale);
    }];

    [oxygenTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-60 * scale);
        make.top.equalTo(pressureTitle);
    }];

    [self.oxygenLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(oxygenTitle);
        make.top.equalTo(oxygenTitle.snp_bottom).offset(8 * scale);
    }];

    // 第二行：日出和日落
    [sunriseTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(pressureTitle);
        make.top.equalTo(self.pressureLabel.snp_bottom).offset(30 * scale);
    }];

    [self.sunriseLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(sunriseTitle);
        make.top.equalTo(sunriseTitle.snp_bottom).offset(8 * scale);
    }];

    [sunsetTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(oxygenTitle);
        make.top.equalTo(sunriseTitle);
    }];

    [self.sunsetLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(sunsetTitle);
        make.top.equalTo(sunsetTitle.snp_bottom).offset(8 * scale);
    }];

    // 第三行：经度和纬度
    [longitudeTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(pressureTitle);
        make.top.equalTo(self.sunriseLabel.snp_bottom).offset(30 * scale);
    }];

    [self.longitudeLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(longitudeTitle);
        make.top.equalTo(longitudeTitle.snp_bottom).offset(8 * scale);
    }];

    [latitudeTitle snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(oxygenTitle);
        make.top.equalTo(longitudeTitle);
    }];

    [self.latitudeLabel snp_makeConstraints:^(ConstraintMaker *make) {
        make.centerX.equalTo(latitudeTitle);
        make.top.equalTo(latitudeTitle.snp_bottom).offset(8 * scale);
    }];
}

- (void)setupLocationManager {
    self.locationManager = [[CLLocationManager alloc] init];
    self.locationManager.delegate = self;
    self.locationManager.desiredAccuracy = kCLLocationAccuracyBest;

    // 请求定位权限
    [self.locationManager requestWhenInUseAuthorization];
}

- (void)setupCompass {
    // 检查设备是否支持磁力计
    if (![CLLocationManager headingAvailable]) {
        NSLog(@"设备不支持磁力计");
        return;
    }

    // 设置磁力计更新
    self.locationManager.headingFilter = 1.0; // 1度的过滤器
    [self.locationManager startUpdatingHeading];
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;

    // 检查定位精度，如果精度太差就不更新
    if (location.horizontalAccuracy > 100) {
        return;
    }

    AltitudeDataManager *dataManager = [AltitudeDataManager sharedManager];

    // 更新海拔显示
    if (location.altitude > -1000) { // 过滤明显错误的海拔数据
        self.altitudeLabel.text = [NSString stringWithFormat:@"%.0fM", location.altitude];

        // 计算并更新气压
        double pressure = [dataManager calculatePressureWithAltitude:location.altitude];
        self.pressureLabel.text = [NSString stringWithFormat:@"%.1fkPa", pressure];

        // 计算并更新含氧量
        double oxygenPercentage = [dataManager calculateOxygenPercentageWithAltitude:location.altitude];
        self.oxygenLabel.text = [NSString stringWithFormat:@"%.0f%%", oxygenPercentage];
    }

    // 更新精度显示
    self.accuracyLabel.text = [NSString stringWithFormat:@"海拔精度:%.0fM", fabs(location.verticalAccuracy)];

    // 更新经纬度显示
    self.longitudeLabel.text = [NSString stringWithFormat:@"%.4f", location.coordinate.longitude];
    self.latitudeLabel.text = [NSString stringWithFormat:@"%.4f", location.coordinate.latitude];

    // 计算并更新日出日落时间
    NSDate *currentDate = [NSDate date];
    NSDate *sunrise = [dataManager calculateSunriseWithLatitude:location.coordinate.latitude
                                                      longitude:location.coordinate.longitude
                                                           date:currentDate];
    NSDate *sunset = [dataManager calculateSunsetWithLatitude:location.coordinate.latitude
                                                     longitude:location.coordinate.longitude
                                                          date:currentDate];

    self.sunriseLabel.text = [dataManager formatTimeFromDate:sunrise];
    self.sunsetLabel.text = [dataManager formatTimeFromDate:sunset];

    // 更新城市名称
    [dataManager getCityNameWithLatitude:location.coordinate.latitude
                               longitude:location.coordinate.longitude
                              completion:^(NSString *cityName) {
        dispatch_async(dispatch_get_main_queue(), ^{
            self.locationLabel.text = cityName;
        });
    }];

    // 更新日期显示
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyy年M月d日";
    self.dateLabel.text = [dateFormatter stringFromDate:currentDate];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    NSLog(@"定位失败: %@", error.localizedDescription);
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            [self.locationManager startUpdatingLocation];
            [self.locationManager startUpdatingHeading];
            self.locationLabel.text = @"正在定位...";
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            NSLog(@"定位权限被拒绝");
            self.locationLabel.text = @"定位权限被拒绝";
            [self showLocationPermissionAlert];
            break;
        case kCLAuthorizationStatusNotDetermined:
            self.locationLabel.text = @"等待定位权限...";
            break;
        default:
            break;
    }
}

- (void)showLocationPermissionAlert {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"需要定位权限"
                                                                   message:@"请在设置中开启定位权限以获取海拔等信息"
                                                            preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *settingsAction = [UIAlertAction actionWithTitle:@"去设置"
                                                             style:UIAlertActionStyleDefault
                                                           handler:^(UIAlertAction * _Nonnull action) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]
                                           options:@{}
                                 completionHandler:nil];
    }];

    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消"
                                                           style:UIAlertActionStyleCancel
                                                         handler:nil];

    [alert addAction:settingsAction];
    [alert addAction:cancelAction];

    [self presentViewController:alert animated:YES completion:nil];
}

- (void)locationManager:(CLLocationManager *)manager didUpdateHeading:(CLHeading *)newHeading {
    // 获取磁北方向角度
    double magneticHeading = newHeading.magneticHeading;

    // 将角度转换为弧度
    double radians = magneticHeading * M_PI / 180.0;

    // 旋转表盘，使其指向北方
    // 注意：我们需要反向旋转表盘，因为我们要让表盘上的北方指向真实的北方
    CGAffineTransform transform = CGAffineTransformMakeRotation(-radians);

    // 使用动画平滑旋转
    [UIView animateWithDuration:0.1 animations:^{
        self.compassView.transform = transform;
        self.northPointer.transform = transform;
        self.southPointer.transform = transform;
    }];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    NSLog(@"定位失败: %@", error.localizedDescription);
}

#pragma mark - Button Actions

- (void)cameraButtonTapped:(UIButton *)sender {
    NSLog(@"拍照按钮被点击");
    // TODO: 实现拍照功能
}

- (void)settingsButtonTapped:(UIButton *)sender {
    NSLog(@"设置按钮被点击");
    // TODO: 跳转到设置页面
}

- (void)trackButtonTapped:(UIButton *)sender {
    NSLog(@"我的足迹按钮被点击");
    // TODO: 跳转到足迹页面
}

@end
