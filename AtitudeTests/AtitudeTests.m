//
//  AtitudeTests.m
//  AtitudeTests
//
//  Created by fs0011 on 2025/8/5.
//

#import <XCTest/XCTest.h>

@interface AtitudeTests : XCTestCase

@end

@implementation AtitudeTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
